import { StyleParser } from './style-parser';

/**
 * 换行处理器 - 专门处理HTML元素的换行逻辑
 */
export class LineBreakProcessor {
  /**
   * 检查元素是否应该作为内联容器处理
   */
  static shouldTreatAsInlineContainer(el: Element): boolean {
    const style = StyleParser.parseStyle(el);

    // 检查是否是简单的span+inline-block结构
    const isSimpleSpanInlineBlockStructure =
      el.children.length === 2 &&
      el.children[0].tagName === 'SPAN' &&
      el.children[1].tagName === 'DIV' &&
      StyleParser.parseStyle(el.children[1] as Element).display ===
        'inline-block';

    // 只有满足特定条件的简单结构才应该走内联分支
    const shouldBeInline =
      isSimpleSpanInlineBlockStructure &&
      style.whiteSpace === 'nowrap' &&
      style.alignItems === 'center';

    // 添加调试日志
    if (
      el.textContent &&
      (el.textContent.includes('一、') ||
        el.textContent.includes('1.') ||
        el.textContent.includes('读一读') ||
        el.textContent.includes('测试题干'))
    ) {
      console.log(`\n[LineBreakProcessor] 检查元素: ${el.tagName}`);
      console.log(
        `  文本内容: "${el.textContent?.trim().substring(0, 30)}..."`
      );
      console.log(`  子元素数量: ${el.children.length}`);
      if (el.children.length > 0) {
        for (let i = 0; i < el.children.length; i++) {
          const child = el.children[i];
          const childStyle = StyleParser.parseStyle(child as Element);
          console.log(
            `    子元素${i}: ${child.tagName}, display=${childStyle.display}`
          );
        }
      }
      console.log(
        `  样式: whiteSpace=${style.whiteSpace}, alignItems=${style.alignItems}`
      );
      console.log(
        `  isSimpleSpanInlineBlockStructure: ${isSimpleSpanInlineBlockStructure}`
      );
      console.log(`  shouldBeInline: ${shouldBeInline}`);
      console.log(
        `  决定: ${shouldBeInline ? '作为内联容器处理' : '作为块级容器处理'}`
      );
    }

    return shouldBeInline;
  }

  /**
   * 检查元素是否应该在前面添加换行
   */
  static shouldAddLineBreakBefore(el: Element): boolean {
    // 如果是内联容器，不添加换行
    if (this.shouldTreatAsInlineContainer(el)) {
      return false;
    }

    // 块级元素通常需要换行
    const isBlockElement =
      el.tagName === 'DIV' ||
      el.tagName === 'P' ||
      el.tagName.match(/^H[1-6]$/);

    // P标签在inline-block容器中不添加换行
    const isPInInlineBlockContainer =
      el.tagName === 'P' &&
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    const shouldAddBreak = isBlockElement && !isPInInlineBlockContainer;

    // 添加调试日志
    if (
      el.textContent &&
      (el.textContent.includes('一、') ||
        el.textContent.includes('1.') ||
        el.textContent.includes('读一读') ||
        el.textContent.includes('测试题干'))
    ) {
      console.log(`\n[LineBreakProcessor] 检查前置换行: ${el.tagName}`);
      console.log(
        `  文本内容: "${el.textContent?.trim().substring(0, 30)}..."`
      );
      console.log(`  isBlockElement: ${isBlockElement}`);
      console.log(`  isPInInlineBlockContainer: ${isPInInlineBlockContainer}`);
      console.log(`  shouldAddBreak: ${shouldAddBreak}`);
    }

    return shouldAddBreak;
  }

  /**
   * 检查元素是否应该在后面添加换行
   */
  static shouldAddLineBreakAfter(el: Element): boolean {
    // 如果是内联容器，不添加换行
    if (this.shouldTreatAsInlineContainer(el)) {
      return false;
    }

    const style = StyleParser.parseStyle(el);

    // 块级元素通常需要换行
    const isBlockElement =
      el.tagName === 'DIV' ||
      el.tagName === 'P' ||
      el.tagName.match(/^H[1-6]$/);

    // P标签在inline-block容器中不添加换行
    const isPInInlineBlockContainer =
      el.tagName === 'P' &&
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    // inline-block DIV不添加换行
    const isInlineBlockDiv =
      el.tagName === 'DIV' && style.display === 'inline-block';

    const shouldAddBreak =
      isBlockElement && !isPInInlineBlockContainer && !isInlineBlockDiv;

    // 添加调试日志
    if (
      el.textContent &&
      (el.textContent.includes('一、') ||
        el.textContent.includes('1.') ||
        el.textContent.includes('读一读') ||
        el.textContent.includes('测试题干'))
    ) {
      console.log(`\n[LineBreakProcessor] 检查后置换行: ${el.tagName}`);
      console.log(
        `  文本内容: "${el.textContent?.trim().substring(0, 30)}..."`
      );
      console.log(`  isBlockElement: ${isBlockElement}`);
      console.log(`  isPInInlineBlockContainer: ${isPInInlineBlockContainer}`);
      console.log(`  isInlineBlockDiv: ${isInlineBlockDiv}`);
      console.log(`  shouldAddBreak: ${shouldAddBreak}`);
    }

    return shouldAddBreak;
  }

  /**
   * 获取元素的处理分支类型（用于调试）
   */
  static getProcessingBranch(el: Element): string {
    const style = StyleParser.parseStyle(el);

    // 检查是否在inline-block容器中
    const isInInlineBlockContainer =
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    // 检查各种条件
    const isActuallyBlock = [
      'div',
      'p',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
    ].includes(el.tagName.toLowerCase());
    const isActuallyInline = [
      'span',
      'a',
      'strong',
      'em',
      'b',
      'i',
      'u',
    ].includes(el.tagName.toLowerCase());

    const isStandardBlockTag =
      isActuallyBlock &&
      !isInInlineBlockContainer &&
      (el.tagName === 'P' || el.tagName.match(/^H[1-6]$/));

    const isInlineBlockOrFlex =
      style.display === 'inline-block' ||
      style.display === 'flex' ||
      style.display === 'inline-flex';

    const hasTextContent = el.textContent && el.textContent.trim().length > 0;
    const isTextContainingDivBlock =
      el.tagName === 'DIV' &&
      hasTextContent &&
      !isInlineBlockOrFlex &&
      !this.shouldTreatAsInlineContainer(el);

    const isDivBlock =
      el.tagName === 'DIV' && !hasTextContent && !isInlineBlockOrFlex;

    // 确定分支
    if (isStandardBlockTag) return 'isStandardBlockTag';
    if (isInlineBlockOrFlex) return 'isInlineBlockOrFlex';
    if (isTextContainingDivBlock) return 'isTextContainingDivBlock';
    if (isDivBlock) return 'isDivBlock';
    if (isActuallyInline || this.shouldTreatAsInlineContainer(el))
      return 'isActuallyInline';
    return 'else';
  }
}
