import { StyleParser } from './style-parser';

/**
 * 换行处理器 - 按照正确的HTML/CSS解析规则处理换行逻辑
 */
export class LineBreakProcessor {
  /**
   * 获取元素的实际类型（考虑display属性的影响）
   */
  static getActualElementType(
    el: Element
  ): 'inline' | 'block' | 'inline-block' {
    const style = StyleParser.parseStyle(el);

    // 如果有display属性，优先使用display属性
    if (style.display === 'inline') return 'inline';
    if (style.display === 'block') return 'block';
    if (style.display === 'inline-block') return 'inline-block';

    // 否则使用元素的默认类型
    const inlineElements = ['SPAN', 'A', 'STRONG', 'EM', 'B', 'I', 'U', 'IMG'];
    const blockElements = [
      'DIV',
      'P',
      'H1',
      'H2',
      'H3',
      'H4',
      'H5',
      'H6',
      'UL',
      'OL',
      'LI',
    ];

    if (inlineElements.includes(el.tagName)) return 'inline';
    if (blockElements.includes(el.tagName)) return 'block';

    return 'inline'; // 默认为内联
  }

  /**
   * 检查元素是否在flex容器中（只影响直接子元素）
   */
  static isDirectChildOfFlexContainer(el: Element): boolean {
    if (!el.parentElement) return false;

    const parentStyle = StyleParser.parseStyle(el.parentElement);
    return (
      parentStyle.display === 'flex' || parentStyle.display === 'inline-flex'
    );
  }

  /**
   * 检查元素是否应该在前面添加换行
   */
  static shouldAddLineBreakBefore(el: Element): boolean {
    // 获取元素的实际类型
    const actualType = this.getActualElementType(el);

    // 如果是flex容器的直接子元素，按内联处理，不添加换行
    if (this.isDirectChildOfFlexContainer(el)) {
      return false;
    }

    // 只有块级元素才可能需要添加换行
    if (actualType !== 'block') {
      return false;
    }

    // P标签在inline-block容器中不添加换行
    const isPInInlineBlockContainer =
      el.tagName === 'P' &&
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    return !isPInInlineBlockContainer;
  }

  /**
   * 检查元素是否应该在后面添加换行
   */
  static shouldAddLineBreakAfter(el: Element): boolean {
    // 获取元素的实际类型
    const actualType = this.getActualElementType(el);

    // 如果是flex容器的直接子元素，按内联处理，不添加换行
    if (this.isDirectChildOfFlexContainer(el)) {
      return false;
    }

    // 只有块级元素才可能需要添加换行
    if (actualType !== 'block') {
      return false;
    }

    // P标签在inline-block容器中不添加换行
    const isPInInlineBlockContainer =
      el.tagName === 'P' &&
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    return !isPInInlineBlockContainer;
  }

  /**
   * 获取元素的处理分支类型（用于调试）
   */
  static getProcessingBranch(el: Element): string {
    const style = StyleParser.parseStyle(el);

    // 检查是否在inline-block容器中
    const isInInlineBlockContainer =
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    // 检查各种条件
    const isActuallyBlock = [
      'div',
      'p',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
    ].includes(el.tagName.toLowerCase());
    const isActuallyInline = [
      'span',
      'a',
      'strong',
      'em',
      'b',
      'i',
      'u',
    ].includes(el.tagName.toLowerCase());

    const isStandardBlockTag =
      isActuallyBlock &&
      !isInInlineBlockContainer &&
      (el.tagName === 'P' || el.tagName.match(/^H[1-6]$/));

    const isInlineBlockOrFlex =
      style.display === 'inline-block' ||
      style.display === 'flex' ||
      style.display === 'inline-flex';

    const hasTextContent = el.textContent && el.textContent.trim().length > 0;
    const isTextContainingDivBlock =
      el.tagName === 'DIV' &&
      hasTextContent &&
      !isInlineBlockOrFlex ;

    const isDivBlock =
      el.tagName === 'DIV' && !hasTextContent && !isInlineBlockOrFlex;

    // 确定分支
    if (isStandardBlockTag) return 'isStandardBlockTag';
    if (isInlineBlockOrFlex) return 'isInlineBlockOrFlex';
    if (isTextContainingDivBlock) return 'isTextContainingDivBlock';
    if (isDivBlock) return 'isDivBlock';
    if (isActuallyInline || this.shouldTreatAsInlineContainer(el))
      return 'isActuallyInline';
    return 'else';
  }
}
