import { StyleParser } from './style-parser';

/**
 * 换行处理器 - 按照正确的HTML/CSS解析规则处理换行逻辑
 */
export class LineBreakProcessor {
  /**
   * 获取元素的实际类型（考虑display属性的影响）
   */
  static getActualElementType(
    el: Element
  ): 'inline' | 'block' | 'inline-block' {
    const style = StyleParser.parseStyle(el);

    // 如果有display属性，优先使用display属性
    if (style.display === 'inline') return 'inline';
    if (style.display === 'block') return 'block';
    if (style.display === 'inline-block') return 'inline-block';

    // 否则使用元素的默认类型
    const inlineElements = ['SPAN', 'A', 'STRONG', 'EM', 'B', 'I', 'U', 'IMG'];
    const blockElements = [
      'DIV',
      'P',
      'H1',
      'H2',
      'H3',
      'H4',
      'H5',
      'H6',
      'UL',
      'OL',
      'LI',
    ];

    if (inlineElements.includes(el.tagName)) return 'inline';
    if (blockElements.includes(el.tagName)) return 'block';

    return 'inline'; // 默认为内联
  }

  /**
   * 检查元素是否在flex容器中（只影响直接子元素）
   */
  static isDirectChildOfFlexContainer(el: Element): boolean {
    if (!el.parentElement) return false;

    const parentStyle = StyleParser.parseStyle(el.parentElement);
    return (
      parentStyle.display === 'flex' || parentStyle.display === 'inline-flex'
    );
  }
  /**
   * 检查元素是否应该作为内联容器处理
   * 这个方法用于判断容器的处理分支，不是判断元素本身的类型
   */
  static shouldTreatAsInlineContainer(el: Element): boolean {
    const style = StyleParser.parseStyle(el);

    // 只有span+inline-block的特殊结构才应该作为内联容器处理
    const isSpanInlineBlockStructure =
      el.children.length === 2 &&
      el.children[0].tagName === 'SPAN' &&
      el.children[1].tagName === 'DIV' &&
      StyleParser.parseStyle(el.children[1] as Element).display ===
        'inline-block' &&
      style.whiteSpace === 'nowrap' &&
      style.alignItems === 'center';

    // 添加调试日志
    if (
      el.textContent &&
      (el.textContent.includes('一、') ||
        el.textContent.includes('1.') ||
        el.textContent.includes('读一读') ||
        el.textContent.includes('测试题干'))
    ) {
      console.log(`\n[LineBreakProcessor] 检查容器处理方式: ${el.tagName}`);
      console.log(
        `  文本内容: "${el.textContent?.trim().substring(0, 30)}..."`
      );
      console.log(`  子元素数量: ${el.children.length}`);
      if (el.children.length > 0) {
        for (let i = 0; i < el.children.length; i++) {
          const child = el.children[i];
          const childStyle = StyleParser.parseStyle(child as Element);
          console.log(
            `    子元素${i}: ${child.tagName}, display=${childStyle.display}`
          );
        }
      }
      console.log(
        `  样式: whiteSpace=${style.whiteSpace}, alignItems=${style.alignItems}`
      );
      console.log(
        `  isSpanInlineBlockStructure: ${isSpanInlineBlockStructure}`
      );
      console.log(
        `  决定: ${
          isSpanInlineBlockStructure ? '作为内联容器处理' : '作为块级容器处理'
        }`
      );
    }

    return isSpanInlineBlockStructure;
  }

  /**
   * 检查元素是否应该在前面添加换行
   */
  static shouldAddLineBreakBefore(el: Element): boolean {
    // 获取元素的实际类型
    const actualType = this.getActualElementType(el);

    // 如果是flex容器的直接子元素，按内联处理，不添加换行
    if (this.isDirectChildOfFlexContainer(el)) {
      return false;
    }

    // 只有块级元素才可能需要添加换行
    if (actualType !== 'block') {
      return false;
    }

    // P标签在inline-block容器中不添加换行
    const isPInInlineBlockContainer =
      el.tagName === 'P' &&
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    const shouldAddBreak = !isPInInlineBlockContainer;

    // 添加调试日志
    if (
      el.textContent &&
      (el.textContent.includes('一、') ||
        el.textContent.includes('1.') ||
        el.textContent.includes('读一读') ||
        el.textContent.includes('测试题干'))
    ) {
      console.log(`\n[LineBreakProcessor] 检查前置换行: ${el.tagName}`);
      console.log(
        `  文本内容: "${el.textContent?.trim().substring(0, 30)}..."`
      );
      console.log(`  actualType: ${actualType}`);
      console.log(
        `  isDirectChildOfFlexContainer: ${this.isDirectChildOfFlexContainer(
          el
        )}`
      );
      console.log(`  isPInInlineBlockContainer: ${isPInInlineBlockContainer}`);
      console.log(`  shouldAddBreak: ${shouldAddBreak}`);
    }

    return shouldAddBreak;
  }

  /**
   * 检查元素是否应该在后面添加换行
   */
  static shouldAddLineBreakAfter(el: Element): boolean {
    // 获取元素的实际类型
    const actualType = this.getActualElementType(el);

    // 如果是flex容器的直接子元素，按内联处理，不添加换行
    if (this.isDirectChildOfFlexContainer(el)) {
      return false;
    }

    // 只有块级元素才可能需要添加换行
    if (actualType !== 'block') {
      return false;
    }

    // P标签在inline-block容器中不添加换行
    const isPInInlineBlockContainer =
      el.tagName === 'P' &&
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    const shouldAddBreak = !isPInInlineBlockContainer;

    // 添加调试日志
    if (
      el.textContent &&
      (el.textContent.includes('一、') ||
        el.textContent.includes('1.') ||
        el.textContent.includes('读一读') ||
        el.textContent.includes('测试题干'))
    ) {
      console.log(`\n[LineBreakProcessor] 检查后置换行: ${el.tagName}`);
      console.log(
        `  文本内容: "${el.textContent?.trim().substring(0, 30)}..."`
      );
      console.log(`  actualType: ${actualType}`);
      console.log(
        `  isDirectChildOfFlexContainer: ${this.isDirectChildOfFlexContainer(
          el
        )}`
      );
      console.log(`  isPInInlineBlockContainer: ${isPInInlineBlockContainer}`);
      console.log(`  shouldAddBreak: ${shouldAddBreak}`);
    }

    return shouldAddBreak;
  }

  /**
   * 获取元素的处理分支类型（用于调试）
   */
  static getProcessingBranch(el: Element): string {
    const style = StyleParser.parseStyle(el);

    // 检查是否在inline-block容器中
    const isInInlineBlockContainer =
      el.parentElement &&
      StyleParser.parseStyle(el.parentElement).display === 'inline-block';

    // 检查各种条件
    const isActuallyBlock = [
      'div',
      'p',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
    ].includes(el.tagName.toLowerCase());
    const isActuallyInline = [
      'span',
      'a',
      'strong',
      'em',
      'b',
      'i',
      'u',
    ].includes(el.tagName.toLowerCase());

    const isStandardBlockTag =
      isActuallyBlock &&
      !isInInlineBlockContainer &&
      (el.tagName === 'P' || el.tagName.match(/^H[1-6]$/));

    const isInlineBlockOrFlex =
      style.display === 'inline-block' ||
      style.display === 'flex' ||
      style.display === 'inline-flex';

    const hasTextContent = el.textContent && el.textContent.trim().length > 0;
    const isTextContainingDivBlock =
      el.tagName === 'DIV' &&
      hasTextContent &&
      !isInlineBlockOrFlex &&
      !this.shouldTreatAsInlineContainer(el);

    const isDivBlock =
      el.tagName === 'DIV' && !hasTextContent && !isInlineBlockOrFlex;

    // 确定分支
    if (isStandardBlockTag) return 'isStandardBlockTag';
    if (isInlineBlockOrFlex) return 'isInlineBlockOrFlex';
    if (isTextContainingDivBlock) return 'isTextContainingDivBlock';
    if (isDivBlock) return 'isDivBlock';
    if (isActuallyInline || this.shouldTreatAsInlineContainer(el))
      return 'isActuallyInline';
    return 'else';
  }
}
