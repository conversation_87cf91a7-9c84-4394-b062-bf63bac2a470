import { WordExportService } from './src/service/word-export.service';
import * as fs from 'fs';
import * as path from 'path';

async function testDebugOutput() {
  try {
    console.log('开始调试测试，输出关键解析信息...');
    
    const wordExportService = new WordExportService();
    
    // 模拟第二大题的结构：inline-block容器中包含多个P标签
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>调试输出测试</title>
</head>
<body>
    <!-- 第一大题：单个P标签 -->
    <div style="align-items: center; white-space: nowrap;">
        <span style="visibility: visible;">1.</span>
        <div style="display: inline-block;">
            <p>测试题干3224</p>
        </div>
    </div>
    
    <!-- 第二大题：多个P标签 -->
    <div style="align-items: center; white-space: nowrap;">
        <span style="visibility: visible;">2.</span>
        <div style="display: inline-block;">
            <p class="MsoNormal">上课了，不同民族的小学生，在同一间教室里学习。</p>
            <p class="MsoNormal">1.这段话先写上课时，校园里十分安静。</p>
            <p class="MsoNormal">2.同学们上课时，窗外是什么样子的？</p>
            <p class="MsoNormal">3.同学们在下课后做了哪些事情？</p>
        </div>
    </div>
    
    <!-- 第三大题：单个H4标签 -->
    <div style="align-items: center; white-space: nowrap;">
        <span style="visibility: visible;">3.</span>
        <div style="display: inline-block;">
            <h4><strong>形近字组词。</strong></h4>
        </div>
    </div>
</body>
</html>`;
    
    console.log('\n=== 开始解析HTML ===');
    
    // 添加调试日志到HTML解析器
    const originalConsoleLog = console.log;
    let logBuffer: string[] = [];
    
    console.log = (...args: any[]) => {
      const message = args.join(' ');
      logBuffer.push(message);
      originalConsoleLog(...args);
    };
    
    const buffer = await wordExportService.exportHtmlToWord(htmlContent, {
      title: '调试输出测试',
      author: '测试',
      margins: {
        top: 1440,
        right: 1440,
        bottom: 1440,
        left: 1440,
      },
      orientation: 'portrait'
    });
    
    // 恢复console.log
    console.log = originalConsoleLog;
    
    // 保存Word文档
    const outputPath = path.join(__dirname, 'test-debug-output-result.docx');
    fs.writeFileSync(outputPath, buffer);
    console.log('Word文档已保存:', outputPath);
    
    console.log('\n=== 解析日志分析 ===');
    
    // 分析日志，查找关键信息
    const relevantLogs = logBuffer.filter(log => 
      log.includes('inline-block') || 
      log.includes('块级元素') || 
      log.includes('换行') ||
      log.includes('段落') ||
      log.includes('测试题干') ||
      log.includes('上课了') ||
      log.includes('形近字')
    );
    
    if (relevantLogs.length > 0) {
      console.log('找到相关解析日志：');
      relevantLogs.forEach((log, index) => {
        console.log(`${index + 1}. ${log}`);
      });
    } else {
      console.log('没有找到相关的解析日志，可能需要添加更多调试信息');
    }
    
    console.log('\n=== 手动分析HTML结构 ===');
    
    // 手动分析HTML结构
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    
    const inlineBlockDivs = doc.querySelectorAll('div[style*="inline-block"]');
    console.log(`找到 ${inlineBlockDivs.length} 个 inline-block 容器：`);
    
    inlineBlockDivs.forEach((div, index) => {
      const blockChildren = Array.from(div.children).filter(child => {
        const tagName = child.tagName.toLowerCase();
        return tagName === 'p' || tagName.match(/^h[1-6]$/);
      });
      
      console.log(`\n容器 ${index + 1}:`);
      console.log(`  - 包含 ${blockChildren.length} 个块级元素`);
      console.log(`  - 块级元素类型: ${blockChildren.map(c => c.tagName).join(', ')}`);
      console.log(`  - 文本内容预览: "${div.textContent?.trim().substring(0, 50)}..."`);
      
      if (blockChildren.length === 1) {
        console.log(`  - 处理策略: 单个块级元素，与序号保持同行`);
      } else if (blockChildren.length > 1) {
        console.log(`  - 处理策略: 多个块级元素，第一个与序号同行，其余换行`);
      }
    });
    
    console.log('\n=== 预期结果分析 ===');
    console.log('根据修复逻辑，预期结果应该是：');
    console.log('✅ 第一大题："1." 和 "测试题干3224" 在同一行（单个P）');
    console.log('✅ 第二大题："2." 和第一段在同一行，但段落间有换行（多个P）');
    console.log('✅ 第三大题："3." 和 "形近字组词。" 在同一行（单个H4）');
    
    console.log('\n请检查生成的Word文档是否符合预期！');
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

testDebugOutput();
